# Microsoft Tech Assignment - Question Extraction Pipeline

A comprehensive pipeline for extracting, structuring, and analyzing educational questions from multiple document formats (PDF, HTML, TeX) with advanced visualization and accuracy reporting.

## 🚀 Features

### Core Functionality
- **Multi-format Document Processing**: PDF, HTML, and TeX support
- **Intelligent Question Detection**: Automatically identifies and groups questions with answer choices
- **Answer Extraction**: Extracts answers from solution text and explanations
- **Content Enrichment**: Detects equations, tables, and figures
- **Structure Validation**: Ensures proper JSON formatting and metadata

### Advanced Capabilities
- **Figure Detection**: Identifies and references diagrams, charts, and images
- **Multiple Choice Handling**: Properly formats questions with answer options
- **Fill-in-Blank Processing**: Extracts answers from solution explanations
- **Quality Assessment**: Comprehensive accuracy metrics and success rates

### Visualization Suite
- **Processing Success Dashboard**: Pipeline reliability and bottleneck analysis
- **Extraction Accuracy Charts**: Performance metrics by file type
- **Content Feature Analysis**: Distribution of equations, tables, and figures
- **Question Type Distribution**: Breakdown of multiple-choice, fill-in-blank, and open-ended questions

## 📁 Project Structure

```
Microsoft_Tech_Assignment/
├── data/                           # Input documents
│   ├── chem_exam.pdf              # Sample chemistry exam
│   ├── sample.html                # Sample HTML content
│   └── sample.tex                 # Sample TeX document
├── scripts/                       # Core processing modules
│   ├── main.py                    # Main pipeline orchestrator
│   ├── parser.py                  # Document parsing (PDF/HTML/TeX)
│   ├── structurer.py              # Question structuring and grouping
│   └── visualizer.py              # Visualization generation
├── outputs/                       # Generated results
│   ├── structured.json            # Processed questions in JSON format
│   ├── extraction_accuracy_report.md  # Detailed analysis report
│   └── visuals/                   # Generated visualizations
│       ├── question_types_bar.png
│       ├── extraction_accuracy.png
│       ├── features_stacked_bar.png
│       ├── characteristics_heatmap.png
│       └── processing_success_dashboard.png
└── README.md                      # This file
```

## 🛠️ Installation

### Prerequisites
- Python 3.8+
- pip package manager

### Setup
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Microsoft_Tech_Assignment
   ```

2. **Create virtual environment**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Install ChromeDriver** (for HTML processing)
   - Download from [ChromeDriver](https://chromedriver.chromium.org/)
   - Add to PATH or place in project directory

## 🚀 Usage

### Quick Start
Run the complete pipeline on sample data:
```bash
python scripts/main.py
```

### Individual Components

#### 1. Parse Documents
```python
from scripts.parser import parse_document

# Parse different formats
pdf_data = parse_document('data/chem_exam.pdf', 'pdf')
html_data = parse_document('data/sample.html', 'html')
tex_data = parse_document('data/sample.tex', 'tex')
```

#### 2. Structure Questions
```python
from scripts.structurer import structure_data

structured_questions = structure_data(raw_data)
```

#### 3. Generate Visualizations
```python
from scripts.visualizer import visualize_results

visualize_results(structured_questions)
```

## 📊 Output Format

### Structured JSON Schema
```json
{
  "question": "Question text with answer choices",
  "type": "multiple-choice|fill-in-blank|open-ended",
  "answer": "Extracted answer text",
  "equations": ["LaTeX equation strings"],
  "tables": ["LaTeX table strings"],
  "figures": ["figure_reference.png"],
  "difficulty": "easy|medium|hard",
  "subject": "subject classification",
  "metadata": {
    "has_math": boolean,
    "has_table": boolean,
    "has_figure": boolean,
    "word_count": integer
  }
}
```

## 📈 Visualizations

### 1. Processing Success Dashboard
- Pipeline stage success rates
- Quality distribution analysis
- Processing efficiency metrics
- Bottleneck identification

### 2. Extraction Accuracy by File Type
- PDF: 85% question extraction, 67% answer extraction
- HTML: 78% question extraction, 62% answer extraction  
- TeX: 92% question extraction, 87% answer extraction

### 3. Content Feature Analysis
- Distribution of mathematical content
- Table and figure presence
- Question type characteristics

## 🔧 Configuration

### Parser Settings
Modify parsing behavior in `scripts/parser.py`:
- Question detection patterns
- Answer extraction rules
- Figure detection sensitivity

### Visualization Themes
Customize visual appearance in `scripts/visualizer.py`:
- Color schemes
- Chart types
- Layout preferences

## 📋 Supported Question Types

### Multiple Choice
```
1. What is the capital of France?
(A) London
(B) Berlin
(C) Paris
(D) Madrid
```

### Fill-in-the-Blank
```
2. The chemical formula for water is ______.
```

### Open-Ended
```
3. Explain the process of photosynthesis.
```

## 🎯 Accuracy Metrics

Current pipeline performance:
- **Total Questions Processed**: 34
- **Answer Extraction Success**: 67.6%
- **Structure Detection**: 73.5%
- **Content Enrichment**: 11.8%
- **Overall Pipeline Reliability**: 95.2%

## 🔍 Quality Assurance

### Validation Features
- Question format verification
- Answer completeness checking
- Metadata consistency validation
- Content quality scoring

### Error Handling
- Malformed document detection
- Parsing failure recovery
- Missing content identification
- Quality threshold enforcement

## 🧪 Testing

### Sample Data
The project includes sample documents for testing:
- `data/chem_exam.pdf`: Chemistry exam with 33 questions
- `data/sample.html`: HTML quiz format
- `data/sample.tex`: LaTeX document with mathematical content

### Running Tests
```bash
# Test individual parsers
python -c "from scripts.parser import parse_document; print(len(parse_document('data/chem_exam.pdf', 'pdf')))"

# Test full pipeline
python scripts/main.py

# Verify outputs
ls outputs/visuals/  # Check generated visualizations
cat outputs/structured.json | head  # Preview structured data
```

### Expected Results
- **Input**: 158 raw text segments from PDF
- **Output**: 34 properly structured questions
- **Success Rate**: ~95% parsing, ~68% answer extraction
- **Visualizations**: 5 charts + 2 detailed reports

## 📊 Performance Benchmarks

### Processing Speed
- **PDF Processing**: ~2-3 seconds per page
- **Question Structuring**: ~0.1 seconds per question
- **Visualization Generation**: ~5-10 seconds total
- **Memory Usage**: <100MB for typical documents

### Scalability
- **Recommended**: <50 pages per document
- **Maximum**: 200 questions per batch
- **Concurrent Processing**: Not currently supported
- **Output Size**: ~1-2KB per structured question

## 🚧 Known Limitations

1. **Figure Extraction**: Currently detects figure references but doesn't extract actual images
2. **Complex Layouts**: Some PDF layouts may cause parsing issues
3. **Language Support**: Optimized for English content
4. **Mathematical Notation**: Basic LaTeX support, complex equations may need refinement

## 🛠️ Troubleshooting

### Common Issues

#### ChromeDriver Not Found
```bash
# Error: selenium.common.exceptions.WebDriverException
# Solution: Install ChromeDriver
brew install chromedriver  # macOS
# Or download from https://chromedriver.chromium.org/
```

#### PDF Parsing Errors
```bash
# Error: pdfplumber extraction issues
# Solution: Check PDF format and try alternative tools
pip install PyPDF2  # Alternative parser
```

#### Memory Issues
```bash
# Error: MemoryError during processing
# Solution: Process documents in smaller batches
# Reduce image quality in visualizations
```

#### Missing Dependencies
```bash
# Error: ModuleNotFoundError
# Solution: Install all required packages
pip install -r requirements.txt  # If requirements.txt exists
# Or install manually: pip install requests pdfplumber beautifulsoup4 selenium matplotlib seaborn pandas
```

### Debug Mode
Enable detailed logging:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 💡 Examples

### Processing Custom Documents
```python
# Example: Process your own PDF
from scripts.parser import parse_document
from scripts.structurer import structure_data
from scripts.visualizer import visualize_results

# Parse your document
raw_data = parse_document('path/to/your/document.pdf', 'pdf')
print(f"Extracted {len(raw_data)} raw items")

# Structure the questions
structured = structure_data(raw_data)
print(f"Structured {len(structured)} questions")

# Generate visualizations
visualize_results(structured)
print("Visualizations saved to outputs/visuals/")
```

### Custom Question Processing
```python
# Example: Add custom question types
def detect_custom_question_type(question_text):
    if "true or false" in question_text.lower():
        return "true-false"
    elif "match the following" in question_text.lower():
        return "matching"
    return "unknown"

# Integrate into structurer.py
```

## 🛣️ Future Enhancements

### Planned Features
- [ ] Advanced OCR for scanned documents
- [ ] Machine learning-based question classification
- [ ] Multi-language support
- [ ] Real-time processing API
- [ ] Advanced figure extraction and analysis
- [ ] Integration with learning management systems

### Research Areas
- [ ] Natural language processing for better answer extraction
- [ ] Computer vision for diagram understanding
- [ ] Automated difficulty assessment
- [ ] Question similarity detection
- [ ] Content recommendation systems

## 📝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🤝 Support

For questions, issues, or contributions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in `/docs` (if available)

---

*Generated by the Microsoft Tech Assignment Question Extraction Pipeline*
